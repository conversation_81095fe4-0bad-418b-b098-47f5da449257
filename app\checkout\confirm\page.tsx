"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useSession, signIn } from "next-auth/react";
import { toast } from "sonner";


// Maximum time to wait before automatically redirecting (in milliseconds)
const AUTO_REDIRECT_DELAY = 6000;

// Get base URL from environment with fallback to the browser's origin
const getBaseUrl = () => {
  if (typeof window !== 'undefined') {
    // Client-side: Use window.location.origin as fallback
    return process.env.NEXT_PUBLIC_BASE_URL || window.location.origin;
  }
  // Server-side: Use environment variable only (shouldn't reach here in client component)
  return process.env.NEXT_PUBLIC_BASE_URL || '';
};

/**
 * This is a fallback page for direct access to /checkout/confirm
 * It should redirect to /checkout/confirmed with all the parameters
 */
export default function CheckoutConfirmPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { status } = useSession();
  const [processingTime, setProcessingTime] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [isRedirecting, setIsRedirecting] = useState(true);
  
  // Extract relevant parameters
  const paymentId = searchParams.get('razorpay_payment_id') as string;
  const orderId = searchParams.get('razorpay_order_id') as string;
  const paymentStatus = searchParams.get('status') as string;
  const errorMessage = searchParams.get('error_message') as string;
  const requestId = searchParams.get('request_id') as string;
  
  // Session verification
  useEffect(() => {
    if (status === "loading") return;
    
    if (status === "unauthenticated") {
      toast.error("You must be signed in to access this page");
      
      // Use Keycloak provider directly for authentication
      signIn("keycloak", { callbackUrl: "/checkout" });
    }
  }, [status]);
  
  // Log the payment details on the client for debugging
  useEffect(() => {
    // Only log if there's something meaningful to log
    if (paymentId || orderId || requestId) {
      console.log("Payment confirmation details detected:", {
        paymentId: paymentId ? `***${paymentId.slice(-4)}` : undefined,
        orderId,
        requestId,
        status: paymentStatus,
        // Include other important params for debugging
        paramsReceived: Object.keys(searchParams),
        origin: window.location.origin,
        envBaseUrl: process.env.NEXT_PUBLIC_BASE_URL
      });
    }
  }, [paymentId, orderId, requestId, paymentStatus, searchParams]);
  
  // Auto-redirect to appropriate page based on payment status
  useEffect(() => {
    if (status === "loading") return;
    
    // Initialize the timer for the progress indicator
    const timer = setInterval(() => {
      setProcessingTime(prev => Math.min(prev + 100, AUTO_REDIRECT_DELAY));
    }, 100);
    
    // Set up the redirect logic
    const redirectTimer = setTimeout(() => {
      if (status === "authenticated") {
        // If this is an error page, show toast with error message
        if (paymentStatus === "error" && errorMessage) {
          toast.error(errorMessage);
          router.push("/checkout");
          return;
        }
        
        // If we have payment details, redirect to success page
        if (paymentId || orderId) {
          // Success case - redirect to confirmed page
          router.push("/checkout/confirmed");
          return;
        }
        
        // No payment details case
        if (!paymentId && !orderId) {
          toast.error("Invalid payment confirmation. Missing payment details.");
          router.push("/checkout");
          return;
        }
      }
    }, AUTO_REDIRECT_DELAY);
    
    return () => {
      clearInterval(timer);
      clearTimeout(redirectTimer);
    };
  }, [paymentId, orderId, status, router, paymentStatus, errorMessage]);
  
  useEffect(() => {
    // If we end up on this page directly, try to redirect to the confirmed page
    if (isRedirecting) {
      try {
        // Create a new URL for the confirmed page using the base URL or fallback
        const baseUrl = getBaseUrl();
        const confirmedUrl = new URL('/checkout/confirmed', baseUrl);
        
        console.log("Redirecting to confirmed page with base URL:", baseUrl);
        
        // Collect all search parameters
        const allParams = searchParams.toString();
        
        if (allParams) {
          // Add success status if it's not already present
          if (!searchParams.has('status')) {
            confirmedUrl.searchParams.append('status', 'success');
          }
          
          // Redirect to the confirmed page with all parameters
          router.replace(`/checkout/confirmed?${allParams}`);
        } else {
          // If there are no parameters, this is likely an error or direct access
          setError("No payment information found. Please try again or contact support.");
          setIsRedirecting(false);
        }
      } catch (error) {
        console.error("Error redirecting to confirmed page:", error);
        setError("An error occurred while processing your payment. Please try again or contact support.");
        setIsRedirecting(false);
      }
    }
  }, [router, searchParams, isRedirecting]);

  // Handle the case when redirect doesn't happen automatically
  const handleManualRedirect = () => {
    // Just go to the confirmed page with error status
    router.push('/checkout/confirmed?status=error&error_message=Manual+redirect+from+confirm+page');
  };

  // Determine the UI state based on parameters
  const isError = paymentStatus === "error";
  const isSuccess = !isError && (paymentId || orderId);
  const progressPercentage = (processingTime / AUTO_REDIRECT_DELAY) * 100;
  
  // If an error occurred during redirect
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="text-center max-w-md">
          <h1 className="text-2xl font-bold mb-4">Payment Processing</h1>
          <p className="text-red-500 mb-6">{error}</p>
          <button
            onClick={handleManualRedirect}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            Continue to Order Summary
          </button>
        </div>
      </div>
    );
  }

  return (
    <main className="container py-16 flex items-center justify-center">
      <div className="text-center max-w-md mx-auto">
        {isError ? (
          // Error state
          <div>
            <div className="h-12 w-12 bg-red-100 rounded-full flex items-center justify-center mx-auto">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold mt-4">Payment Processing Error</h2>
            <p className="mt-2 text-muted-foreground">
              {errorMessage || "We encountered an issue while processing your payment."}
            </p>
            <div className="mt-4">
              <button 
                onClick={() => router.push("/checkout")}
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
              >
                Return to Checkout
              </button>
            </div>
            {requestId && (
              <p className="text-xs text-muted-foreground mt-6">
                Reference ID: {requestId}
              </p>
            )}
          </div>
        ) : isSuccess ? (
          // Success state (payment ID present)
          <div>
            <div className="animate-spin h-10 w-10 border-4 border-primary border-t-transparent rounded-full mx-auto"></div>
            <h2 className="text-xl font-semibold mt-4">Verifying Payment</h2>
            <p className="mt-2 text-muted-foreground">
              Your payment is being processed and verified.
              {paymentId ? " We've received your payment details." : ""}
            </p>
            
            {/* Progress bar */}
            <div className="w-full h-2 bg-gray-200 rounded-full mt-6 overflow-hidden">
              <div 
                className="h-full bg-primary rounded-full transition-all duration-100 ease-linear"
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>
            
            <p className="text-xs text-muted-foreground mt-4">
              You&apos;ll be redirected automatically in a few seconds...
            </p>
            
            {requestId && (
              <p className="text-xs text-muted-foreground mt-6">
                Transaction ID: {paymentId ? paymentId.slice(-6) : ""} • Reference: {requestId}
              </p>
            )}
          </div>
        ) : (
          // Default processing state (no payment ID)
          <div>
            <div className="animate-spin h-10 w-10 border-4 border-primary border-t-transparent rounded-full mx-auto"></div>
            <h2 className="text-xl font-semibold mt-4">Processing</h2>
            <p className="mt-2 text-muted-foreground">
              Please wait while we confirm your payment details.
            </p>
            
            {/* Progress bar */}
            <div className="w-full h-2 bg-gray-200 rounded-full mt-6 overflow-hidden">
              <div 
                className="h-full bg-primary rounded-full transition-all duration-100 ease-linear"
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>
            
            <p className="text-xs text-muted-foreground mt-4">
              You&apos;ll be redirected automatically once we confirm your payment...
            </p>
          </div>
        )}
      </div>
    </main>
  );
}
