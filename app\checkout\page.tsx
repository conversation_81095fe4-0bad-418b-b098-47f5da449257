"use client";

import { useState, useEffect } from "react";
import { useCart } from "@/context/cart-context";
import { formatPrice } from "@/lib/utils";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import { INDIAN_STATES } from "@/lib/constants";
import { Check, CreditCard, Info, ShoppingBag, Trash2 } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { RemoveItemDialog } from "@/components/cart/remove-item-dialog";
import { useSession, signIn } from "next-auth/react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

// Import our new services
import { resolveKeycloakId } from "@/src/services/keycloakService";
import { checkCustomerById, checkCustomerByEmail, getOrCreateCustomer, CustomerData } from "@/src/services/lago/customerService";
import { initiatePayment, PaymentRequestData, PaymentItem } from "@/src/services/paymentService";

// Form validation schema - simplified for virtual products
const formSchema = z.object({
  // Billing information
  fullName: z.string().min(3, "Full name is required"),
  email: z.string().email("Valid email is required"),
  phone: z.string().regex(/^[6-9]\d{9}$/, "Valid 10-digit Indian phone number is required"),
  address1: z.string().min(5, "Address is required"),
  address2: z.string().optional(),
  city: z.string().min(2, "City is required"),
  state: z.string().min(2, "State is required"),
  pincode: z.string().regex(/^[1-9][0-9]{5}$/, "Valid 6-digit PIN code is required"),

  // Tax identification details
  gstNumber: z.string()
    .optional()
    .transform(val => val ? val.trim().toUpperCase() : val) // Normalize Tax Identification number
    .refine(val => !val || /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/.test(val), {
      message: "Invalid Tax Identification Number format (e.g., 22AAAAA0000A1Z5)"
    }),
  businessName: z.string().optional()
    .transform(val => val ? val.trim() : val), // Trim legal name

  // Terms acceptance
  acceptTerms: z.boolean().refine(val => val === true, {
    message: "You must accept the terms and conditions"
  }),
}).refine(data => !data.gstNumber || data.businessName, {
  message: "Legal name is required when Tax Identification Number is provided",
  path: ["businessName"],
});

const CheckoutPage = () => {
  const { items, subtotal, cgst, igst, total, removeItem, setIsOpen } = useCart();
  const [itemToRemove, setItemToRemove] = useState<{ id: string; name: string } | null>(null);
  const { data: session, status } = useSession();
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const router = useRouter();

  // Check authentication status and redirect if not authenticated
  useEffect(() => {
    if (status === "loading") return;

    if (status === "unauthenticated" || !session) {
      toast.error("You must be signed in to access the checkout page");

      // Use Keycloak provider directly for authentication
      signIn("keycloak", { callbackUrl: "/checkout" });
    }
  }, [status, session]);

  // If cart is empty, redirect to home page
  useEffect(() => {
    if (items.length === 0 && status !== "loading") {
      toast.info("Your cart is empty. Please add items before proceeding to checkout.");
      router.push("/");
    }
  }, [items, router, status]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: "",
      email: "",
      phone: "",
      address1: "",
      address2: "",
      city: "",
      state: "",
      pincode: "",
      gstNumber: "",
      businessName: "",
      acceptTerms: false,
    },
  });

  // Calculate rounding adjustment (to nearest whole rupee)
  const calculatedTotal = subtotal + cgst + igst;
  const roundedTotal = Math.round(calculatedTotal);
  const roundingAdjustment = roundedTotal - calculatedTotal;

  // Close mini-cart when checkout page loads and populate form with session data
  useEffect(() => {
    setIsOpen(false);
    if (session) {
      // Populate form fields from session data
      form.setValue("fullName", session.user?.name || "");
      form.setValue("email", session.user?.email || "");

      // Add phone number from session if available
      if (session.phone) {
        form.setValue("phone", session.phone);
      }
      else{
        form.setValue("phone", "");
      }

      // Optionally populate additional fields if available
      if (session.given_name && session.family_name) {
        form.setValue("fullName", `${session.given_name} ${session.family_name}`);
      }

      // Fetch customer data including Tax Identification Number and legal name
      const fetchCustomerData = async () => {
        try {
          const { keycloakId } = resolveKeycloakId({
            session,
            useStorage: true
          });

          if (keycloakId) {
            const customerData = await checkCustomerById(keycloakId);
            if (customerData) {
              // Set Tax Identification Number if available
              if (customerData.tax_identification_number) {
                form.setValue("gstNumber", customerData.tax_identification_number);
              }

              // Set legal name if available
              if (customerData.legal_name) {
                form.setValue("businessName", customerData.legal_name);
              }
            }
          }
        } catch (error) {
          console.error("❌ CHECKOUT - Error fetching customer data:", error);
        }
      };

      fetchCustomerData();
    }
  }, [setIsOpen, session, form]);

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsProcessing(true);
      setPaymentError(null);

      // CRITICAL: Get the Keycloak ID from various sources
      const { keycloakId, source, isValid } = resolveKeycloakId({
        session,
        useStorage: true
      });

      if (!keycloakId) {
        console.error("❌ CHECKOUT - No Keycloak ID available");
        setPaymentError("Unable to identify your account. Please contact support.");
        return;
      }

      console.log(`🔍 CHECKOUT - Using Keycloak ID from ${source}: ${keycloakId} (Valid UUID: ${isValid ? 'Yes' : 'No'})`);

      // Store this for debugging
      try {
        localStorage.setItem('checkout_keycloak_id', keycloakId);
        localStorage.setItem('checkout_keycloak_source', source);
      } catch {
        // Ignore storage errors
      }

      // 1. Check if the customer exists in Lago by email (for reference only)
      const { exists: existsByEmail, customerId: existingId } = await checkCustomerByEmail(values.email);
      if (existsByEmail) {
        console.log(`ℹ️ CHECKOUT - Customer with email ${values.email} exists with ID: ${existingId}`);
        console.log(`ℹ️ CHECKOUT - Will still use Keycloak ID: ${keycloakId} for consistency`);
      }

      // 2. Check if customer exists by Keycloak ID
      const existsById = await checkCustomerById(keycloakId);

      // 3. If not exists, create the customer with the Keycloak ID
      if (!existsById) {
        console.log(`🔄 CHECKOUT - Customer does not exist with Keycloak ID, creating new customer`);

        const customerData: CustomerData = {
          external_id: keycloakId,
          name: values.fullName,
          email: values.email,
          address_line1: values.address1,
          address_line2: values.address2 || undefined,
          city: values.city,
          state: values.state,
          zipcode: values.pincode,
          country: "IN",
          currency: "INR",
          tax_identification_number: values.gstNumber || undefined,
          legal_name: values.businessName || undefined,
          phone: values.phone,
          customer_type: "individual"
        };

        const createResult = await getOrCreateCustomer(customerData);
        if (!createResult) {
          console.error(`❌ CHECKOUT - Failed to create customer`);
          setPaymentError("Error creating customer record. Please try again or contact support.");
          return;
        }

        console.log(`✅ CHECKOUT - Customer created/updated successfully`);
      } else {
        console.log(`✅ CHECKOUT - Customer exists with Keycloak ID: ${keycloakId}`);

        // Check if Tax Identification Number or legal name has changed and update if needed
        const existingCustomer = await checkCustomerById(keycloakId);

        console.log(`📝 TAX FLOW - Existing customer data:`, {
          keycloakId,
          existingTaxId: existingCustomer?.tax_identification_number || 'None',
          existingLegalName: existingCustomer?.legal_name || 'None',
          newTaxId: values.gstNumber || 'None',
          newLegalName: values.businessName || 'None'
        });

        if (existingCustomer && (
            existingCustomer.tax_identification_number !== values.gstNumber ||
            existingCustomer.legal_name !== values.businessName
          )) {
          console.log(`📝 TAX FLOW - Tax info needs update for customer ID: ${keycloakId}`);
          console.log(`📝 TAX FLOW - Old Tax ID: ${existingCustomer.tax_identification_number || 'None'} → New Tax ID: ${values.gstNumber || 'None'}`);
          console.log(`📝 TAX FLOW - Old Legal Name: ${existingCustomer.legal_name || 'None'} → New Legal Name: ${values.businessName || 'None'}`);

          // Import the updateCustomerTaxInfo function
          const { updateCustomerGstInfo } = await import('@/src/services/lago/customerService');

          // Update customer with new tax info
          console.log(`📝 TAX FLOW - Calling updateCustomerGstInfo with:`, {
            keycloakId,
            taxId: values.gstNumber || 'undefined',
            legalName: values.businessName || 'undefined'
          });

          const updateResult = await updateCustomerGstInfo(
            keycloakId,
            values.gstNumber || undefined,
            values.businessName || undefined
          );

          if (!updateResult) {
            console.error(`❌ TAX FLOW - Failed to update customer tax info`);
            // If the error is because the customer doesn't exist, create the customer first
            if (existingCustomer) {
              console.log(`🔄 TAX FLOW - Customer exists but update failed, continuing with payment process`);
              // Continue with payment process even if update fails
            } else {
              console.log(`🔄 TAX FLOW - Customer may not exist, attempting to create customer first`);
              // Try to create the customer first
              const customerData: CustomerData = {
                external_id: keycloakId,
                name: values.fullName,
                email: values.email,
                address_line1: values.address1,
                address_line2: values.address2 || undefined,
                city: values.city,
                state: values.state,
                zipcode: values.pincode,
                country: "IN",
                currency: "INR",
                tax_identification_number: values.gstNumber || undefined,
                legal_name: values.businessName || undefined,
                phone: values.phone,
                customer_type: "individual"
              };

              const createResult = await getOrCreateCustomer(customerData);
              if (createResult) {
                console.log(`✅ TAX FLOW - Customer created successfully, continuing with payment process`);
              } else {
                console.error(`❌ TAX FLOW - Failed to create customer, continuing with payment process anyway`);
              }
            }
          } else {
            console.log(`✅ TAX FLOW - Customer tax info updated successfully:`, {
              tax_identification_number: updateResult.tax_identification_number || 'None',
              legal_name: updateResult.legal_name || 'None'
            });
          }
        } else if (existingCustomer) {
          console.log(`📝 TAX FLOW - No tax info changes needed for customer ID: ${keycloakId}`);
        }
      }

      // 4. Prepare payment data using the Keycloak ID
      const product = items[0]; // Get first cart item for display

      // Map cart items to payment items
      const paymentItems: PaymentItem[] = items.map(item => ({
        id: item.id,
        name: item.name,
        productName: item.productName,
        price: item.price,
        planCode: item.planCode,
        planDuration: item.planDuration
      }));

      // Create payment request data
      const paymentData: PaymentRequestData = {
        fullName: values.fullName,
        email: values.email,
        phone: values.phone,
        address1: values.address1,
        address2: values.address2,
        city: values.city,
        state: values.state,
        pincode: values.pincode,
        gstNumber: values.gstNumber,
        businessName: values.businessName,
        product: product?.productName || "OneBiz Subscription",
        price: roundedTotal,
        keycloakId: keycloakId,
        items: paymentItems
      };

      // 5. Initiate payment
      console.log(`🔄 CHECKOUT - Initiating payment for Keycloak ID: ${keycloakId}`);
      const paymentResult = await initiatePayment(paymentData);

      if (paymentResult.error) {
        console.error(`❌ CHECKOUT - Payment initiation failed:`, paymentResult.error);
        setPaymentError(paymentResult.error);
        return;
      }

      if (!paymentResult.url) {
        console.error(`❌ CHECKOUT - No payment URL returned`);
        setPaymentError("Payment gateway did not provide a redirect URL. Please try again.");
        return;
      }

      // 6. Redirect to payment gateway
      console.log(`✅ CHECKOUT - Payment initiated, redirecting to payment gateway`);
      window.location.href = paymentResult.url;

    } catch (error) {
      console.error(`❌ CHECKOUT - Exception during checkout:`, error);
      setPaymentError("An unexpected error occurred. Please try again or contact support.");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRemoveClick = (id: string, name: string) => {
    setItemToRemove({ id, name });
  };

  const handleRemoveConfirm = () => {
    if (itemToRemove) {
      removeItem(itemToRemove.id);
      setItemToRemove(null);
    }
  };

  return (
    <>
      <div className="container max-w-6xl py-8 md:py-12 lg:py-16">
        <div className="flex flex-col lg:flex-row gap-6 lg:gap-8">
          {/* Left side - Form */}
          <div className="flex-1">
            <div className="mb-8">
              <Link href="/" className="flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground">
                <ShoppingBag size={16} />
                <span>Continue Shopping</span>
              </Link>
              <h1 className="mt-4 text-2xl md:text-3xl font-bold">Checkout</h1>
              <p className="text-muted-foreground mt-1">Complete your purchase by providing your billing details.</p>
            </div>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="space-y-4">
                  <h2 className="text-xl font-semibold">Billing Information</h2>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="fullName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Full Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter your full name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center">
                            <FormLabel>Email</FormLabel>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="h-5 w-5 rounded-full ml-2"
                                  >
                                    <Info size={12} />
                                    <span className="sr-only">Info</span>
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>To change your email address, please contact support.</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="Your email address"
                              {...field}
                              readOnly
                              className="bg-muted/30 cursor-not-allowed"
                            />
                          </FormControl>

                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone Number</FormLabel>
                        <FormControl>
                          <Input placeholder="10-digit mobile number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="address1"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Address Line 1</FormLabel>
                        <FormControl>
                          <Input placeholder="Street address" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="address2"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Address Line 2 (Optional)</FormLabel>
                        <FormControl>
                          <Input placeholder="Apartment, suite, building, etc." {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="city"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>City</FormLabel>
                          <FormControl>
                            <Input placeholder="City" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="state"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>State</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select state" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {INDIAN_STATES.map((state) => (
                                <SelectItem key={state} value={state}>
                                  {state}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="pincode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>PIN Code</FormLabel>
                          <FormControl>
                            <Input placeholder="6-digit PIN" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="space-y-4 border border-muted rounded-lg p-4 bg-muted/20">
                    <h3 className="text-sm font-medium">Tax Information (Optional)</h3>

                    <FormField
                      control={form.control}
                      name="gstNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Tax Identification Number
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Tax Identification Number"
                              {...field}
                              onChange={(e) => {
                                // Convert to uppercase as user types
                                field.onChange(e.target.value.toUpperCase());
                              }}
                            />
                          </FormControl>
                          <FormDescription>
                            Format: 22AAAAA0000A1Z5 (Will appear on invoice)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Show legal name field only when Tax Identification Number has a value */}
                    {form.watch("gstNumber") && (
                      <FormField
                        control={form.control}
                        name="businessName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              Legal Name <span className="text-destructive">*</span>
                            </FormLabel>
                            <FormControl>
                              <Input placeholder="Registered legal name" {...field} />
                            </FormControl>
                            <FormDescription>
                              Legal name as per Tax Identification registration (Will appear on invoice)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </div>

                  <FormField
                    control={form.control}
                    name="acceptTerms"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 mt-6">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>
                            I agree to the <Link href="/terms" className="text-primary underline">Terms of Service</Link> and <Link href="/privacy" className="text-primary underline">Privacy Policy</Link>
                          </FormLabel>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>

                {paymentError && (
                  <div className="bg-destructive/10 border border-destructive/20 rounded-md p-3">
                    <p className="text-destructive text-sm">{paymentError}</p>
                  </div>
                )}

                <Button
                  type="submit"
                  className="w-full flex items-center justify-center gap-2"
                  disabled={items.length === 0 || isProcessing}
                >
                  {isProcessing ? (
                    <>
                      <span className="animate-spin mr-2">⏳</span>
                      Processing...
                    </>
                  ) : (
                    <>
                      <CreditCard size={18} />
                      Proceed to Pay {total > 0 && `(${formatPrice(roundedTotal)})`}
                    </>
                  )}
                </Button>

                {/* Payment security indicator */}
                <div className="flex items-center justify-center text-xs text-muted-foreground gap-1 mt-2">
                  <Check size={14} className="text-green-600" />
                  <span>Secure payment processing</span>
                </div>
              </form>
            </Form>
          </div>

          {/* Right side - Order Summary */}
          <div className="lg:w-2/5 bg-muted/30 rounded-lg p-6 sm:p-8 h-fit">
            <h2 className="text-xl font-semibold mb-6">Order Summary</h2>

            <div className="space-y-6">
              {/* Products */}
              <div className="space-y-5">
                {items.map((item) => (
                  <div key={item.id} className="flex gap-4 pb-5 border-b border-muted-foreground/10 last:border-0 last:pb-0">
                    {/* Product image */}
                    <div className="bg-background rounded-md w-16 h-16 overflow-hidden flex-shrink-0 flex items-center justify-center p-2 border border-muted/30">
                      {item.productLogo ? (
                        <Image
                          src={item.productLogo}
                          alt={item.name}
                          width={56}
                          height={56}
                          className="object-contain"
                          unoptimized
                        />
                      ) : (
                        <div className="w-full h-full bg-muted flex items-center justify-center">
                          <ShoppingBag size={20} className="text-muted-foreground" />
                        </div>
                      )}
                    </div>

                    {/* Product details */}
                    <div className="flex-1 flex flex-col min-w-0">
                      {/* Product header with name and remove button */}
                      <div className="flex justify-between items-start">
                        <h4 className="font-medium break-words pr-2 pt-0.5">
                          {item.productName ? `${item.productName}` : item.name}
                        </h4>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7 text-muted-foreground hover:bg-muted/60 -mr-1.5 -mt-0.5 transition-colors"
                          onClick={() => handleRemoveClick(item.id, item.productName || item.name)}
                          title="Remove item"
                        >
                          <Trash2 size={15} />
                        </Button>
                      </div>

                      {/* Plan details */}
                      <div className="space-y-0.5 mt-0.5">
                        <div className="text-sm text-muted-foreground">
                          Plan: <span className="font-medium">{item.name}</span>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Duration: {item.planDuration === "monthly" ? "Monthly" : "Yearly"}
                        </div>
                      </div>

                      {/* Price */}
                      <div className="mt-2 flex justify-end">
                        <span className="font-medium">{formatPrice(item.price)}</span>
                      </div>
                    </div>
                  </div>
                ))}

                {items.length === 0 && (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <ShoppingBag className="h-12 w-12 text-muted-foreground/30 mb-3" />
                    <p className="text-muted-foreground mb-2">Your cart is empty</p>
                    <Button asChild variant="link" className="mt-2">
                      <Link href="/">Continue Shopping</Link>
                    </Button>
                  </div>
                )}
              </div>

              {items.length > 0 && (
                <>
                  <Separator className="my-2" />

                  {/* Price Breakdown */}
                  <div className="space-y-3 py-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Subtotal</span>
                      <span>{formatPrice(subtotal)}</span>
                    </div>

                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">CGST (9%)</span>
                      <span>{formatPrice(cgst)}</span>
                    </div>

                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">IGST (9%)</span>
                      <span>{formatPrice(igst)}</span>
                    </div>

                    {/* Add Rounding Adjustment */}
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Rounding Adjustment</span>
                      <span>{formatPrice(roundingAdjustment)}</span>
                    </div>
                  </div>

                  <Separator className="my-2" />

                  {/* Total */}
                  <div className="flex justify-between py-3">
                    <span className="text-lg font-semibold">Total</span>
                    <span className="text-lg font-semibold text-primary">{formatPrice(roundedTotal)}</span>
                  </div>
                </>
              )}

              {/* Digital product note */}
              {/* <div className="text-sm bg-primary/10 rounded-md p-3 border border-primary/20">
                <p className="flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                    <polyline points="7 10 12 15 17 10" />
                    <line x1="12" x2="12" y1="15" y2="3" />
                  </svg>
                  <span>Digital product - Instant delivery after payment</span>
                </p>
              </div> */}

              {/* Secure payment message */}
              <div className="text-xs text-muted-foreground text-center mt-4 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                  <rect width="18" height="16" x="3" y="4" rx="2" />
                  <path d="M8 11v5" />
                  <path d="M16 11v5" />
                  <path d="M12 11v5" />
                </svg>
                <span>Secure Checkout</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      {itemToRemove && (
        <RemoveItemDialog
          isOpen={!!itemToRemove}
          onClose={() => setItemToRemove(null)}
          onConfirm={handleRemoveConfirm}
          itemName={itemToRemove.name}
        />
      )}
    </>
  );
};

export default CheckoutPage;
