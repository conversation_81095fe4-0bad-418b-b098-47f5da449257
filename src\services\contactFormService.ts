/**
 * Contact Form Service
 *
 * Handles all contact form-related functionality, including sending contact form
 * submissions through the MailForm service.
 */

// Constants for API endpoints - these would typically come from environment variables
// For development/testing, we're using a direct API endpoint
const MAILFORM_API_URL = process.env.NEXT_PUBLIC_MAILFORM_API_URL || '/api/contact';
const MAILFORM_API_KEY = process.env.MAILFORM_API_KEY;

// reCAPTCHA configuration - secret key should only be used server-side
// const RECAPTCHA_SECRET_KEY = process.env.RECAPTCHA_SECRET_KEY;

// Store sent form IDs to prevent duplicates
// In-memory cache for the current server instance
const sentForms = new Set<string>();

/**
 * Interface for contact form data
 */
export interface ContactFormData {
  firstName: string;
  lastName: string;
  email: string;
  subject: string;
  message: string;
  recaptchaToken?: string;
}

/**
 * Interface for contact form response
 */
export interface ContactFormResponse {
  success: boolean;
  message: string;
  error?: string;
  formId?: string;
}

/**
 * Generate a unique form ID based on form data
 *
 * @param formData The contact form data
 * @returns A unique ID for the form submission
 */
function generateFormId(formData: ContactFormData): string {
  const { email } = formData;
  return `form_${email}_${Date.now()}`;
}

/**
 * Check if a form has already been submitted to prevent duplicates
 *
 * @param formId The form ID to check
 * @returns Boolean indicating if the form has already been submitted
 */
function hasFormBeenSubmitted(formId: string): boolean {
  // Check in-memory cache
  if (sentForms.has(formId)) {
    return true;
  }

  // For client-side, also check localStorage
  if (typeof window !== 'undefined' && window.localStorage) {
    try {
      const sentFormsJson = localStorage.getItem('sent_contact_forms');
      if (sentFormsJson) {
        const sentFormsArray = JSON.parse(sentFormsJson);
        if (Array.isArray(sentFormsArray) && sentFormsArray.includes(formId)) {
          // Add to in-memory cache as well
          sentForms.add(formId);
          return true;
        }
      }
    } catch (error) {
      console.error('Error checking localStorage for sent forms:', error);
    }
  }

  return false;
}

/**
 * Mark a form as submitted to prevent duplicates
 *
 * @param formId The form ID to mark as submitted
 */
function markFormAsSubmitted(formId: string): void {
  // Add to in-memory cache
  sentForms.add(formId);

  // For client-side, also update localStorage
  if (typeof window !== 'undefined' && window.localStorage) {
    try {
      const sentFormsJson = localStorage.getItem('sent_contact_forms');
      let sentFormsArray = sentFormsJson ? JSON.parse(sentFormsJson) : [];

      if (!Array.isArray(sentFormsArray)) {
        sentFormsArray = [];
      }

      if (!sentFormsArray.includes(formId)) {
        sentFormsArray.push(formId);
        localStorage.setItem('sent_contact_forms', JSON.stringify(sentFormsArray));
      }
    } catch (error) {
      console.error('Error updating localStorage for sent forms:', error);
    }
  }
}

/**
 * Send a contact form submission (client-side)
 *
 * @param formData The contact form data
 * @returns Promise with the form submission result
 */
export async function sendContactForm(
  formData: ContactFormData
): Promise<ContactFormResponse> {
  if (!formData) {
    console.error('❌ CONTACT FORM SERVICE - Cannot send form: Missing form data');
    return {
      success: false,
      message: 'Missing form data',
      error: 'MISSING_FORM_DATA'
    };
  }

  console.log('📝 CONTACT FORM - Sending contact form submission');

  try {
    // Call the server-side API route to avoid CORS issues
    const response = await fetch('/api/contact', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(formData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('❌ CONTACT FORM - Submission failed:', errorData);
      return {
        success: false,
        message: errorData.message || 'Failed to send contact form',
        error: errorData.error || 'API_ERROR'
      };
    }

    const data = await response.json();
    console.log('✅ CONTACT FORM - Submission successful:', data);

    return {
      success: true,
      message: data.message || 'Contact form sent successfully',
      formId: data.formId
    };
  } catch (error) {
    console.error('❌ CONTACT FORM - Exception in sendContactForm:', error);
    return {
      success: false,
      message: 'An error occurred while sending the contact form',
      error: error instanceof Error ? error.message : 'UNKNOWN_ERROR'
    };
  }
}

/**
 * Send a contact form submission (server-side)
 *
 * This version is for server-side use and directly calls the MailForm API
 *
 * @param formData The contact form data
 * @param trackingId Optional tracking ID for logging
 * @returns Promise with the form submission result
 */
export async function sendContactFormServer(
  formData: ContactFormData,
  trackingId?: string
): Promise<ContactFormResponse> {
  if (!formData) {
    console.error('❌ CONTACT FORM SERVICE - Cannot send form: Missing form data');
    return {
      success: false,
      message: 'Missing form data',
      error: 'MISSING_FORM_DATA'
    };
  }

  const formId = generateFormId(formData);

  // Check if this form has already been submitted to prevent duplicates
  if (hasFormBeenSubmitted(formId)) {
    console.log(`📝 CONTACT FORM - Form ${formId} already submitted, skipping duplicate`);
    return {
      success: true,
      message: 'Form already submitted (deduplicated)',
      formId: formId
    };
  }

  const logPrefix = trackingId ? `📝 CONTACT FORM [${trackingId}]` : '📝 CONTACT FORM';
  const requestId = `form_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;

  console.log(`${logPrefix} [${requestId}] - Sending contact form submission (server)`);
  console.log(`${logPrefix} [${requestId}] - Using MailForm API URL: ${MAILFORM_API_URL}`);

  try {
    // Prepare the request payload for MailForm
    const payload = {
      from: formData.email,
      firstName: formData.firstName,
      lastName: formData.lastName,
      subject: formData.subject,
      body: formData.message,
      // Add current year for the email template footer
      year: new Date().getFullYear().toString()
    };

    // Add reCAPTCHA token if available
    if (formData.recaptchaToken) {
      // Use the appropriate field name based on the captcha provider
      // For reCAPTCHA, use 'g-recaptcha-response'
      // For hCaptcha, use 'h-captcha-response'
      payload['g-recaptcha-response'] = formData.recaptchaToken;
    }

    console.log(`${logPrefix} [${requestId}] - Request payload:`, payload);

    // Set up headers
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Request-ID': requestId
    };

    // Add API key if available
    if (MAILFORM_API_KEY) {
      headers['Authorization'] = `Bearer ${MAILFORM_API_KEY}`;
      console.log(`${logPrefix} [${requestId}] - Using Authorization Bearer token`);
    }

    // For development/testing, we'll simulate a successful response
    // In production, this would call the actual MailForm API
    console.log(`${logPrefix} [${requestId}] - Development mode: Simulating successful response`);

    // Simulate a delay to mimic network latency
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Mark the form as submitted to prevent duplicates
    markFormAsSubmitted(formId);

    // Return a mock successful response
    return {
      success: true,
      message: 'Contact form sent successfully (development mode)',
      formId
    };
  } catch (error) {
    console.error(`${logPrefix} [${requestId}] - Exception in sendContactFormServer:`, error);
    return {
      success: false,
      message: 'An error occurred while sending the contact form',
      error: error instanceof Error ? error.message : 'UNKNOWN_ERROR',
      formId
    };
  }
}

/**
 * Retry sending a contact form with exponential backoff
 *
 * @param formData The contact form data
 * @param maxRetries Maximum number of retry attempts
 * @param trackingId Optional tracking ID for logging
 * @returns Promise with the form submission result
 */
export async function sendContactFormWithRetry(
  formData: ContactFormData,
  maxRetries: number = 3,
  trackingId?: string
): Promise<ContactFormResponse> {
  const logPrefix = trackingId ? `📝 CONTACT FORM [${trackingId}]` : '📝 CONTACT FORM';

  let lastError: ContactFormResponse = {
    success: false,
    message: 'All retry attempts failed',
    error: 'MAX_RETRIES_EXCEEDED'
  };

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      console.log(`${logPrefix} - Retry attempt ${attempt + 1}/${maxRetries}`);

      // Wait with exponential backoff before retrying (except first attempt)
      if (attempt > 0) {
        const delayMs = Math.min(1000 * Math.pow(2, attempt - 1), 10000); // Max 10 seconds
        console.log(`${logPrefix} - Waiting ${delayMs}ms before retry attempt ${attempt + 1}`);
        await new Promise(resolve => setTimeout(resolve, delayMs));
      }

      // Attempt to send the form
      const result = await sendContactFormServer(formData, trackingId);

      if (result.success) {
        console.log(`${logPrefix} - Form sent successfully on attempt ${attempt + 1}/${maxRetries}`);
        return result;
      }

      // Store the error for the next iteration
      lastError = result;
      console.log(`${logPrefix} - Attempt ${attempt + 1}/${maxRetries} failed: ${result.message}`);
    } catch (error) {
      console.error(`${logPrefix} - Exception in retry attempt ${attempt + 1}/${maxRetries}:`, error);
      lastError = {
        success: false,
        message: 'An error occurred during retry',
        error: error instanceof Error ? error.message : 'RETRY_ERROR'
      };
    }
  }

  console.error(`${logPrefix} - All ${maxRetries} retry attempts failed`);
  return lastError;
}
