"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useSubscriptionDetails } from "@/src/hooks/useSubscriptionDetails";
import { formatDate, getRemainingDays } from "@/src/utils/date-utils";
import { Clock, CheckCircle, AlertTriangle, XCircle } from "lucide-react";

interface SubscriptionCardProps {
  planCode: string;
  status: string;
  startedAt?: string | null;
  expiresAt?: string | null;
  onManage?: () => void;
  onRenew?: () => void;
}

export function SubscriptionCard({
  planCode,
  status,
  startedAt,
  expiresAt,
  onManage,
  onRenew,
}: SubscriptionCardProps) {
  const subscription = useSubscriptionDetails(planCode, status, startedAt, expiresAt);

  if (!subscription) {
    return null;
  }

  const getStatusBadge = () => {
    switch (subscription.status) {
      case "active":
        return <Badge variant="success" className="ml-2 gap-1"><CheckCircle className="w-3 h-3" /> Active</Badge>;
      case "pending":
        return <Badge variant="warning" className="ml-2 gap-1"><Clock className="w-3 h-3" /> Pending</Badge>;
      case "canceled":
        return <Badge variant="destructive" className="ml-2 gap-1"><XCircle className="w-3 h-3" /> Canceled</Badge>;
      case "terminated":
        return <Badge variant="outline" className="ml-2 gap-1"><XCircle className="w-3 h-3" /> Terminated</Badge>;
      default:
        return <Badge variant="secondary" className="ml-2">{subscription.status}</Badge>;
    }
  };

  const getPlanTypeBadge = () => {
    if (!subscription.planType) return null;
    
    const variant = subscription.planType === "premium" ? "default" : 
                    subscription.planType === "enterprise" ? "premium" : "secondary";
    
    return (
      <Badge variant={variant as "default" | "secondary" | "destructive" | "outline"} className="mr-2">
        {subscription.planTypeName}
      </Badge>
    );
  };

  const getPlanDurationBadge = () => {
    if (!subscription.planDuration) return null;
    
    const variant = subscription.planDuration === "yearly" ? "success" : "outline";
    
    return (
      <Badge variant={variant}>
        {subscription.planDurationName}
      </Badge>
    );
  };

  const getRemainingTime = () => {
    if (!subscription.expiresAt) return null;
    
    const remaining = getRemainingDays(subscription.expiresAt);
    
    if (remaining <= 0) {
      return (
        <div className="text-destructive flex items-center mt-2">
          <AlertTriangle className="w-4 h-4 mr-1" /> Expired
        </div>
      );
    }
    
    if (remaining <= 7) {
      return (
        <div className="text-warning flex items-center mt-2">
          <AlertTriangle className="w-4 h-4 mr-1" /> Expires soon ({remaining} days left)
        </div>
      );
    }
    
    return (
      <div className="text-muted-foreground mt-2">
        {remaining} days remaining
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="flex items-center flex-wrap">
            {subscription.productName}
            {getStatusBadge()}
          </CardTitle>
        </div>
        <CardDescription className="flex flex-wrap gap-1 mt-1">
          {getPlanTypeBadge()}
          {getPlanDurationBadge()}
          {subscription.isTrial && (
            <Badge variant="warning">Trial</Badge>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-1">
          {subscription.startedAt && (
            <div className="text-sm">
              <span className="text-muted-foreground">Started: </span>
              {formatDate(subscription.startedAt)}
            </div>
          )}
          {subscription.expiresAt && (
            <div className="text-sm">
              <span className="text-muted-foreground">Expires: </span>
              {formatDate(subscription.expiresAt)}
            </div>
          )}
          {getRemainingTime()}
        </div>
      </CardContent>
      <CardFooter className="flex gap-2">
        {onManage && (
          <Button variant="outline" onClick={onManage}>
            Manage
          </Button>
        )}
        {onRenew && (
          <Button 
            disabled={subscription.status === "active" || subscription.status === "pending"}
            onClick={onRenew}
          >
            Renew
          </Button>
        )}
      </CardFooter>
    </Card>
  );
} 