"use client";

import { <PERSON><PERSON><PERSON><PERSON>, AlertCircle, ArrowLeft } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import { formatPrice } from "@/lib/utils";
import { useSearchParams, useRouter } from "next/navigation";
import { useUser } from "@/context/user-context";
import { useCart } from "@/context/cart-context";
import { createSubscription } from "@/src/services/subscriptionService";
import { useEffect, useState, useCallback, useRef } from "react";
import { toast } from "sonner";
import { useSession, signIn } from "next-auth/react";
import {
  updateInvoicePaymentStatus,
  getPendingInvoicesForCustomer
} from "@/src/services/lago/invoiceService";
import { AutoPaymentStatusUpdater } from "@/components/payment/auto-payment-status-updater";

// REMOVED: Import of components that cause duplicate API calls
// import { PaymentSuccessListener } from "@/components/payment/payment-success-listener";
// import { PaymentSuccessHandler } from "@/components/payment/payment-success-handler";

interface PaymentDetails {
  status: "success" | "failure" | "pending";
  paymentId: string;
  orderId: string;
  amount: number;
  gateway: string;
  email?: string;
  name?: string;
  date: string;
}

// Global activation tracking to prevent duplicate activations across rerenders and component instances
const GLOBAL_ACTIVATION_STATE = {
  PROCESSED_PAYMENTS: new Set<string>(),
  CURRENT_ACTIVATIONS: new Map<string, boolean>()
};

export default function PaymentConfirmedPage() {
  const searchParams = useSearchParams();
  const [paymentDetails, setPaymentDetails] = useState<PaymentDetails | null>(null);
  const { setUserInfo, userId, userEmail } = useUser();
  const { items, clearCart } = useCart();
  const [isManuallyActivating, setIsManuallyActivating] = useState(false);
  const [manualActivationSuccess, setManualActivationSuccess] = useState(false);
  const { status: sessionStatus } = useSession();
  const router = useRouter();

  // Add a ref to track if activation is already in progress or completed
  const activationStateRef = useRef({
    hasAttemptedActivation: false,
    isActivationInProgress: false,
    activationCompleted: false,
    lastActivationTimestamp: 0,
    activationCount: 0
  });

  // Add a ref for the payment ID to prevent closure issues
  const paymentIdRef = useRef<string | null>(null);

  // Flag to ensure we only initialize once
  const initializedRef = useRef(false);

  // Check authentication status and redirect if not authenticated
  useEffect(() => {
    if (sessionStatus === "loading") return;

    if (sessionStatus === "unauthenticated") {
      toast.error("You must be signed in to access this page");

      // Use Keycloak provider directly for authentication
      signIn("keycloak", { callbackUrl: "/" });
    }
  }, [sessionStatus]);

  // Check for required payment parameters
  useEffect(() => {
    if (sessionStatus === "loading") return;

    const paymentId = searchParams.get("razorpay_payment_id");
    const orderId = searchParams.get("razorpay_order_id");

    if ((!paymentId && !orderId) && sessionStatus === "authenticated") {
      toast.error("Invalid payment confirmation. Missing payment details.");
      router.push("/");
    }
  }, [searchParams, sessionStatus, router]);

  // Function to check if a payment has already been activated
  const isPaymentAlreadyActivated = useCallback((paymentId: string): boolean => {
    if (!paymentId) return false;

    // First check global tracking state
    if (GLOBAL_ACTIVATION_STATE.PROCESSED_PAYMENTS.has(paymentId)) {
      console.log(`✅ ACTIVATION - Payment ${paymentId} found in global tracking`);
      return true;
    }

    // Check session storage (for current browser tab)
    if (sessionStorage.getItem('has_completed_activation') === 'true') {
      console.log(`✅ ACTIVATION - Found completed activation in session storage for payment ${paymentId}`);
      return true;
    }

    // Check localStorage for a more permanent record
    if (localStorage.getItem(`subscription_activated_${paymentId}`) === 'true') {
      console.log(`✅ ACTIVATION - Found record of previous activation for payment ${paymentId}`);
      return true;
    }

    // Check the processed payments array
    try {
      const processedPayments = JSON.parse(localStorage.getItem('processed_payments') || '[]');
      if (Array.isArray(processedPayments) && processedPayments.includes(paymentId)) {
        console.log(`✅ ACTIVATION - Payment ${paymentId} found in processed payments list`);
        return true;
      }
    } catch (error) {
      console.error('❌ ACTIVATION - Error checking processed payments:', error);
    }

    return false;
  }, []);

  // Mark a payment as activated to prevent duplicates
  const markPaymentAsActivated = useCallback((paymentId: string | null) => {
    if (!paymentId) return;

    // Add to global activation tracking
    GLOBAL_ACTIVATION_STATE.PROCESSED_PAYMENTS.add(paymentId);
    GLOBAL_ACTIVATION_STATE.CURRENT_ACTIVATIONS.delete(paymentId);

    try {
      // Add to localStorage array
      const processedPayments = JSON.parse(localStorage.getItem('processed_payments') || '[]');
      if (!processedPayments.includes(paymentId)) {
        processedPayments.push(paymentId);
        localStorage.setItem('processed_payments', JSON.stringify(processedPayments));
      }

      // Set individual flags
      sessionStorage.setItem('has_completed_activation', 'true');
      localStorage.setItem(`subscription_activated_${paymentId}`, 'true');

      console.log(`🔒 ACTIVATION - Marked payment ${paymentId} as processed in all storage locations`);
    } catch (error) {
      console.error('❌ ACTIVATION - Error storing processed payment:', error);
    }
  }, []);

  // Core function to handle subscription activation
  const handleSubscriptionActivation = useCallback(async (
    paymentId: string,
    resolvedUserId: string,
    userEmailValue: string | undefined | null,
    trackingId: string
  ) => {
    console.log(`🚀 ACTIVATION [${trackingId}] - Starting subscription activation for ${paymentId}`);

    // Safety check: already processed?
    if (isPaymentAlreadyActivated(paymentId)) {
      console.log(`✅ ACTIVATION [${trackingId}] - Payment already activated, exiting`);
      setManualActivationSuccess(true);
      activationStateRef.current.activationCompleted = true;
      return;
    }

    // Safety check: already in progress?
    if (GLOBAL_ACTIVATION_STATE.CURRENT_ACTIVATIONS.get(paymentId)) {
      console.log(`⚠️ ACTIVATION [${trackingId}] - Activation in progress by another handler`);
      return;
    }

    // Update state
    activationStateRef.current.isActivationInProgress = true;
    GLOBAL_ACTIVATION_STATE.CURRENT_ACTIVATIONS.set(paymentId, true);
    setIsManuallyActivating(true);

    try {
      // Get cart items
      const savedCartItemsJson = localStorage.getItem("payment_cart_items");
      const cartItems = savedCartItemsJson ? JSON.parse(savedCartItemsJson) : items;

      if (!cartItems || cartItems.length === 0) {
        console.error(`❌ ACTIVATION [${trackingId}] - No cart items found`);
        toast.error("No items found to activate");
        return;
      }

      let successCount = 0;
      let latestSubscriptionTimestamp = null;

      // Process each cart item
      for (const item of cartItems) {
        if (!item.planCode) {
          console.error(`❌ ACTIVATION [${trackingId}] - Item missing plan code:`, item);
          continue;
        }

        try {
          console.log(`🔄 ACTIVATION [${trackingId}] - Creating subscription for ${item.name}`);
          // Convert null to undefined to match API expectations
          const subscriptionResult = await createSubscription(resolvedUserId, item);
          console.log(`✅ ACTIVATION [${trackingId}] - Subscription created for ${item.name}`);

          // Store the subscription creation timestamp for invoice lookup
          if (subscriptionResult?.subscription?.created_at) {
            latestSubscriptionTimestamp = subscriptionResult.subscription.created_at;
          }

          successCount++;
        } catch (error) {
          console.error(`❌ ACTIVATION [${trackingId}] - Failed to create subscription:`, error);
        }
      }

      // Handle results
      if (successCount > 0) {
        console.log(`✅ ACTIVATION [${trackingId}] - Successfully activated ${successCount} subscriptions`);

        // Step 2: Update invoice payment status
        console.log(`🔄 ACTIVATION [${trackingId}] - Checking for pending invoices to update`);

        // Wait for invoice generation
        console.log(`🔄 ACTIVATION [${trackingId}] - Waiting for invoice generation...`);
        await new Promise(resolve => setTimeout(resolve, 5000)); // 5 second delay

        // Format date for invoice lookup
        const formattedDate = latestSubscriptionTimestamp ?
          new Date(latestSubscriptionTimestamp).toISOString().split('T')[0] :
          new Date().toISOString().split('T')[0];

        // Get pending invoices
        const pendingInvoices = await getPendingInvoicesForCustomer(resolvedUserId, formattedDate);

        if (pendingInvoices && pendingInvoices.length > 0) {
          console.log(`🔄 ACTIVATION [${trackingId}] - Found ${pendingInvoices.length} pending invoices to update`);

          // Update each invoice
          let invoiceUpdateSuccess = 0;

          for (const invoice of pendingInvoices) {
            try {
              console.log(`🔄 ACTIVATION [${trackingId}] - Updating invoice ${invoice.lago_id}`);
              const updateResult = await updateInvoicePaymentStatus(invoice.lago_id);

              if (updateResult) {
                console.log(`✅ ACTIVATION [${trackingId}] - Successfully updated invoice ${invoice.lago_id}`);
                invoiceUpdateSuccess++;
              } else {
                console.error(`❌ ACTIVATION [${trackingId}] - Failed to update invoice ${invoice.lago_id}`);
              }
            } catch (error) {
              console.error(`❌ ACTIVATION [${trackingId}] - Error updating invoice:`, error);
            }
          }

          if (invoiceUpdateSuccess > 0) {
            console.log(`✅ ACTIVATION [${trackingId}] - Updated ${invoiceUpdateSuccess}/${pendingInvoices.length} invoices`);
            toast.success(`Updated ${invoiceUpdateSuccess} invoice payment status`);
          } else {
            console.error(`❌ ACTIVATION [${trackingId}] - Failed to update any invoices`);
            toast.error("Failed to update invoice payment status. Please contact support.");
          }
        } else {
          console.log(`ℹ️ ACTIVATION [${trackingId}] - No pending invoices found to update`);
        }

        setManualActivationSuccess(true);
        activationStateRef.current.activationCompleted = true;
        toast.success(`Successfully activated ${successCount} subscription(s)`);

        // Mark this payment as processed
        markPaymentAsActivated(paymentId);

        // Clear the cart after successful subscription activation
        console.log(`🛒 ACTIVATION [${trackingId}] - Clearing cart after successful subscription activation`);
        clearCart();

        // Also clear cart items from localStorage to ensure consistency
        try {
          localStorage.removeItem("payment_cart_items");
          localStorage.removeItem("cart");
          console.log(`✅ ACTIVATION [${trackingId}] - Successfully cleared cart after subscription activation`);
        } catch (error) {
          console.error(`❌ ACTIVATION [${trackingId}] - Error clearing cart from localStorage:`, error);
        }
      } else {
        console.error(`❌ ACTIVATION [${trackingId}] - All subscription activations failed`);
        toast.error("Failed to activate subscriptions. Please contact support.");
      }

    } catch (error) {
      console.error(`❌ ACTIVATION [${trackingId}] - Error during activation:`, error);
      toast.error("Error activating subscriptions. Please contact support.");
    } finally {
      setIsManuallyActivating(false);
      activationStateRef.current.isActivationInProgress = false;
      GLOBAL_ACTIVATION_STATE.CURRENT_ACTIVATIONS.delete(paymentId);
    }
  }, [isPaymentAlreadyActivated, items, markPaymentAsActivated, clearCart]);

  // Initialize component and trigger activation if needed
  useEffect(() => {
    // Skip if already initialized or auth is loading
    if (initializedRef.current || sessionStatus === "loading") return;

    // Set the initialized flag
    initializedRef.current = true;

    // Parse search parameters
    const paymentId = searchParams.get("razorpay_payment_id") || "";
    const signature = searchParams.get("razorpay_signature") || "";
    const gateway = searchParams.get("selected_gateway") || "Razorpay";
    const orderId = searchParams.get("razorpay_order_id") || "";
    const amountRaw = searchParams.get("amount") || "0";
    const amount = Number(amountRaw) / 100; // Convert from paise to rupees
    const email = searchParams.get("email") || undefined;
    const name = searchParams.get("name") || undefined;
    const token = searchParams.get("_token") || "";

    // Store payment ID in ref
    paymentIdRef.current = paymentId;

    // Generate a unique tracking ID for this activation attempt
    const activationId = `init-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

    console.log(`🔍 ACTIVATION [${activationId}] - Initializing component with payment ID: ${paymentId}`);

    // Check if already activated
    if (paymentId && isPaymentAlreadyActivated(paymentId)) {
      console.log(`✅ ACTIVATION [${activationId}] - Payment ${paymentId} already processed`);
      setManualActivationSuccess(true);
      activationStateRef.current.activationCompleted = true;

      // Still set payment details for display
      const status = paymentId && signature ? "success" : "failure";
      setPaymentDetails({
        status,
        paymentId,
        orderId,
        amount,
        gateway,
        email,
        name,
        date: new Date().toLocaleString(),
      });

      return;
    }

    // Get Keycloak ID from various sources
    const keycloakIdFromPayment = searchParams.get("keycloak_id") || searchParams.get("customer_id") || null;
    const keycloakIdFromContext = userId;

    // Try to extract from token
    const keycloakIdFromToken = (() => {
      if (!token) return null;
      try {
        const parts = token.split('.');
        if (parts.length === 3) {
          const payload = JSON.parse(atob(parts[1]));
          return payload.sub || null;
        }
      } catch (e) {
        console.error("❌ ACTIVATION - Error extracting sub from token:", e);
      }
      return null;
    })();

    // Try localStorage as last resort
    const keycloakIdFromStorage = (localStorage.getItem("is_keycloak_user") === "true"
      ? localStorage.getItem("user_id")
      : null);

    // Resolve with priority
    const resolvedKeycloakId = keycloakIdFromPayment ||
      keycloakIdFromToken ||
      keycloakIdFromContext ||
      keycloakIdFromStorage;

    console.log(`🔑 ACTIVATION [${activationId}] - User identity info:`, {
      fromPayment: keycloakIdFromPayment,
      fromToken: keycloakIdFromToken,
      fromContext: keycloakIdFromContext,
      fromStorage: keycloakIdFromStorage,
      resolved: resolvedKeycloakId
    });

    // Set up user info if available
    if (resolvedKeycloakId) {
      console.log(`✅ ACTIVATION [${activationId}] - Setting user info with ID: ${resolvedKeycloakId}`);
      localStorage.setItem("is_keycloak_user", "true");
      setUserInfo(resolvedKeycloakId, email, name);
    } else {
      console.error(`❌ ACTIVATION [${activationId}] - No Keycloak ID found. Activation may fail.`);
      toast.error("Account linking issue detected. Please contact support with your payment ID.");
    }

    // Set payment details
    const status = paymentId && signature ? "success" : "failure";
    setPaymentDetails({
      status,
      paymentId,
      orderId,
      amount,
      gateway,
      email,
      name,
      date: new Date().toLocaleString(),
    });

    // Store payment details for debugging and to trigger automatic subscription activation
    try {
      const now = Date.now();

      // Store payment details for debugging
      localStorage.setItem("last_payment_details", JSON.stringify({
        paymentId, orderId, amount, email,
        keycloakId: resolvedKeycloakId,
        timestamp: now
      }));

      // CRITICAL: Set these values to trigger automatic subscription activation
      if (status === "success") {
        console.log(`🔔 ACTIVATION [${activationId}] - Setting payment success flags in localStorage`);
        localStorage.setItem("payment_success_timestamp", now.toString());
        localStorage.setItem("payment_status", "succeeded");
        localStorage.setItem("last_payment_id", paymentId || "");

        // Ensure cart items are available for activation
        if (!localStorage.getItem("payment_cart_items") && items && items.length > 0) {
          console.log(`📦 ACTIVATION [${activationId}] - Storing cart items for activation`);
          try {
            localStorage.setItem("payment_cart_items", JSON.stringify(items));
          } catch (e) {
            console.error(`❌ ACTIVATION [${activationId}] - Failed to store cart items:`, e);
          }
        }
      }
    } catch (e) {
      console.error("❌ ACTIVATION - Error saving payment details:", e);
    }

    // CRITICAL: Automatically activate subscription if successful payment and we have user ID
    if (status === "success" && paymentId && resolvedKeycloakId) {
      console.log(`🚀 ACTIVATION [${activationId}] - Scheduling automatic activation in 1.5s`);

      // Mark as attempted
      activationStateRef.current.hasAttemptedActivation = true;

      // Ensure cart items are available for activation
      const savedItems = localStorage.getItem("cart_items");
      if (!savedItems) {
        // If no cart items found in normal storage, try to use items from payment context
        const paymentCartItems = localStorage.getItem("payment_cart_items");
        if (!paymentCartItems) {
          console.log(`📦 ACTIVATION [${activationId}] - Storing current cart items for activation`);
          try {
            // Store current cart items for activation
            localStorage.setItem("payment_cart_items", JSON.stringify(items));
          } catch (e) {
            console.error(`❌ ACTIVATION [${activationId}] - Failed to store cart items:`, e);
          }
        }
      }

      // Use setTimeout to ensure the component is fully mounted
      setTimeout(() => {
        // Double-check it's not already activated
        if (!isPaymentAlreadyActivated(paymentId) && !activationStateRef.current.isActivationInProgress) {
          console.log(`🔄 ACTIVATION [${activationId}] - Executing activation now`);
          handleSubscriptionActivation(paymentId, resolvedKeycloakId, email, activationId);
        } else {
          console.log(`✅ ACTIVATION [${activationId}] - Activation already handled`);
          setManualActivationSuccess(true);
        }
      }, 1500);
    }

  }, [searchParams, sessionStatus, userId, setUserInfo, isPaymentAlreadyActivated, handleSubscriptionActivation, items]);

  // Manual activation handler for developer button
  const handleManualActivation = useCallback(() => {
    const paymentId = paymentIdRef.current || (paymentDetails?.paymentId || "");
    if (!paymentId) {
      console.error('❌ MANUAL ACTIVATION - Cannot activate: Missing payment ID');
      toast.error("Missing payment ID for activation");
      return;
    }

    if (!userId) {
      console.error('❌ MANUAL ACTIVATION - Cannot activate: Missing user ID');
      toast.error("Unable to activate: User ID not available");
      return;
    }

    // Force reset activation state for this payment first
    console.log(`🔄 MANUAL ACTIVATION - Clearing activation state for ${paymentId}`);
    GLOBAL_ACTIVATION_STATE.PROCESSED_PAYMENTS.delete(paymentId);
    GLOBAL_ACTIVATION_STATE.CURRENT_ACTIVATIONS.delete(paymentId);
    activationStateRef.current.activationCompleted = false;
    activationStateRef.current.hasAttemptedActivation = false;
    activationStateRef.current.isActivationInProgress = false;

    // Ensure cart items are available
    const savedItems = localStorage.getItem("cart_items");
    if (!savedItems && !localStorage.getItem("payment_cart_items")) {
      console.log('📦 MANUAL ACTIVATION - Storing current cart items');
      try {
        localStorage.setItem("payment_cart_items", JSON.stringify(items));
      } catch (e) {
        console.error('❌ MANUAL ACTIVATION - Failed to store cart items:', e);
      }
    }

    const trackingId = `manual-${Date.now()}`;
    console.log(`🚀 MANUAL ACTIVATION [${trackingId}] - Forcing activation for payment ${paymentId}`);

    // Clear any stored flags that might prevent activation
    try {
      sessionStorage.removeItem('has_completed_activation');
      localStorage.removeItem(`subscription_activated_${paymentId}`);
      // Remove from processed payments array
      const processedPayments = JSON.parse(localStorage.getItem('processed_payments') || '[]');
      const updatedPayments = processedPayments.filter((id: string) => id !== paymentId);
      localStorage.setItem('processed_payments', JSON.stringify(updatedPayments));
    } catch (error) {
      console.error('❌ MANUAL ACTIVATION - Error clearing stored flags:', error);
    }

    // Email can be null - convert to undefined if needed in the handler function
    setIsManuallyActivating(true);
    toast.info("Manually activating subscription...");

    // Call subscription API directly, bypassing some checks
    try {
      const directActivation = async () => {
        const savedCartItemsJson = localStorage.getItem("payment_cart_items");
        const cartItems = savedCartItemsJson ? JSON.parse(savedCartItemsJson) : items;

        if (!cartItems || cartItems.length === 0) {
          toast.error("No items found to activate");
          setIsManuallyActivating(false);
          return;
        }

        let successCount = 0;
        let latestSubscriptionTimestamp = null;

        // Process each cart item
        for (const item of cartItems) {
          if (!item.planCode) {
            console.error(`❌ MANUAL ACTIVATION - Item missing plan code:`, item);
            continue;
          }

          try {
            console.log(`🔄 MANUAL ACTIVATION - Creating subscription for ${item.name}`);
            const subscriptionResult = await createSubscription(userId, item);
            console.log(`✅ MANUAL ACTIVATION - Subscription created for ${item.name}`);

            // Store the subscription creation timestamp for invoice lookup
            if (subscriptionResult?.subscription?.created_at) {
              latestSubscriptionTimestamp = subscriptionResult.subscription.created_at;
            }

            successCount++;
          } catch (error) {
            console.error(`❌ MANUAL ACTIVATION - Failed to create subscription:`, error);
          }
        }

        if (successCount > 0) {
          console.log(`✅ MANUAL ACTIVATION - Successfully activated ${successCount} subscriptions`);

          // Step 2: Update invoice payment status
          console.log(`🔄 MANUAL ACTIVATION - Checking for pending invoices to update`);

          // Wait for invoice generation
          console.log(`🔄 MANUAL ACTIVATION - Waiting for invoice generation...`);
          await new Promise(resolve => setTimeout(resolve, 5000)); // 5 second delay

          // Format date for invoice lookup
          const formattedDate = latestSubscriptionTimestamp ?
            new Date(latestSubscriptionTimestamp).toISOString().split('T')[0] :
            new Date().toISOString().split('T')[0];

          // Get pending invoices
          const pendingInvoices = await getPendingInvoicesForCustomer(userId, formattedDate);

          if (pendingInvoices && pendingInvoices.length > 0) {
            console.log(`🔄 MANUAL ACTIVATION - Found ${pendingInvoices.length} pending invoices to update`);

            // Update each invoice
            let invoiceUpdateSuccess = 0;

            for (const invoice of pendingInvoices) {
              try {
                console.log(`🔄 MANUAL ACTIVATION - Updating invoice ${invoice.lago_id}`);
                const updateResult = await updateInvoicePaymentStatus(invoice.lago_id);

                if (updateResult) {
                  console.log(`✅ MANUAL ACTIVATION - Successfully updated invoice ${invoice.lago_id}`);
                  invoiceUpdateSuccess++;
                } else {
                  console.error(`❌ MANUAL ACTIVATION - Failed to update invoice ${invoice.lago_id}`);
                }
              } catch (error) {
                console.error(`❌ MANUAL ACTIVATION - Error updating invoice:`, error);
              }
            }

            if (invoiceUpdateSuccess > 0) {
              console.log(`✅ MANUAL ACTIVATION - Updated ${invoiceUpdateSuccess}/${pendingInvoices.length} invoices`);
              toast.success(`Updated ${invoiceUpdateSuccess} invoice payment status`);
            } else {
              console.error(`❌ MANUAL ACTIVATION - Failed to update any invoices`);
              toast.error("Failed to update invoice payment status. Please contact support.");
            }
          } else {
            console.log(`ℹ️ MANUAL ACTIVATION - No pending invoices found to update`);
          }

          setManualActivationSuccess(true);
          activationStateRef.current.activationCompleted = true;
          toast.success(`Successfully activated ${successCount} subscription(s)`);
          markPaymentAsActivated(paymentId);

          // Clear the cart after successful subscription activation
          console.log(`🛒 MANUAL ACTIVATION - Clearing cart after successful subscription activation`);
          clearCart();

          // Also clear cart items from localStorage to ensure consistency
          try {
            localStorage.removeItem("payment_cart_items");
            localStorage.removeItem("cart");
            console.log(`✅ MANUAL ACTIVATION - Successfully cleared cart after subscription activation`);
          } catch (error) {
            console.error(`❌ MANUAL ACTIVATION - Error clearing cart from localStorage:`, error);
          }
        } else {
          toast.error("Failed to activate any subscriptions");
        }

        setIsManuallyActivating(false);
      };

      directActivation();
    } catch (error) {
      console.error(`❌ MANUAL ACTIVATION - Unexpected error:`, error);
      toast.error("Error during activation. Please try again.");
      setIsManuallyActivating(false);
    }

  }, [paymentDetails, userId, userEmail, items, markPaymentAsActivated, clearCart]);

  // Function to reset activation state (for developer use)
  const resetActivationState = useCallback((paymentId: string) => {
    if (!paymentId) return;

    console.log(`🔄 ACTIVATION RESET - Clearing activation state for ${paymentId}`);

    // Clear global state
    GLOBAL_ACTIVATION_STATE.PROCESSED_PAYMENTS.delete(paymentId);
    GLOBAL_ACTIVATION_STATE.CURRENT_ACTIVATIONS.delete(paymentId);

    // Clear local state
    activationStateRef.current.activationCompleted = false;
    activationStateRef.current.hasAttemptedActivation = false;
    activationStateRef.current.isActivationInProgress = false;

    // Clear storage flags
    try {
      sessionStorage.removeItem('has_completed_activation');
      localStorage.removeItem(`subscription_activated_${paymentId}`);

      // Remove from processed payments array
      const processedPayments = JSON.parse(localStorage.getItem('processed_payments') || '[]');
      const updatedPayments = processedPayments.filter((id: string) => id !== paymentId);
      localStorage.setItem('processed_payments', JSON.stringify(updatedPayments));

      setManualActivationSuccess(false);
      toast.success("Activation state reset. You can now retry activation.");
    } catch (error) {
      console.error('❌ ACTIVATION RESET - Error clearing activation state:', error);
    }
  }, []);

  if (!paymentDetails) {
    return (
      <div className="container max-w-3xl py-12 flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <div className="container max-w-3xl py-12 md:py-16 lg:py-20">
      {/* REMOVED: External components that cause duplicate API calls */}

      {/* Debugging support - show diagnostic info in development */}
      {process.env.NODE_ENV === 'development' && paymentDetails?.status === "success" && (
        <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md text-xs text-yellow-800">
          <p className="font-semibold">Developer Info:</p>
          <p>Payment ID: {paymentDetails.paymentId || 'N/A'}</p>
          <p>Order ID: {paymentDetails.orderId || 'N/A'}</p>
          <p>User ID: {userId || localStorage.getItem('user_id') || 'N/A'}</p>
          <p className="text-green-700 font-medium">Activation status: {
            manualActivationSuccess || GLOBAL_ACTIVATION_STATE.PROCESSED_PAYMENTS.has(paymentDetails.paymentId)
              ? '✅ Complete'
              : isManuallyActivating
                ? '⏳ In Progress'
                : '⏳ Pending'
          }</p>

          <div className="flex gap-2 mt-2">
            {/* Emergency manual activation button for developers */}
            {!manualActivationSuccess &&
             !GLOBAL_ACTIVATION_STATE.PROCESSED_PAYMENTS.has(paymentDetails.paymentId) && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleManualActivation}
                disabled={isManuallyActivating || !userId || activationStateRef.current.activationCompleted}
              >
                {isManuallyActivating ? 'Activating...' : 'Manual Activate (Fallback)'}
              </Button>
            )}

            {/* Reset button to clear activation state */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => resetActivationState(paymentDetails.paymentId)}
              disabled={isManuallyActivating}
            >
              Reset Activation State
            </Button>
          </div>

          {(manualActivationSuccess || GLOBAL_ACTIVATION_STATE.PROCESSED_PAYMENTS.has(paymentDetails.paymentId)) && (
            <p className="mt-2 text-green-600 font-medium">✓ Subscription activated</p>
          )}
        </div>
      )}

      <div className="mb-8">
        <Link href="/" className="inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground">
          <ArrowLeft size={16} />
          <span>Return to Home</span>
        </Link>
      </div>

      {/* Auto Payment Status Updater - invisible component that automatically updates payment status */}
      {userId && paymentDetails?.status === "success" && (
        <AutoPaymentStatusUpdater
          dateFrom={new Date().toISOString().split('T')[0]}
          enabled={true}
          paymentId={paymentDetails.paymentId}
        />
      )}

      <Card className="border-2 shadow-sm">
        <CardHeader className={`${
          paymentDetails.status === "success"
            ? "bg-green-50 border-b border-green-100"
            : "bg-red-50 border-b border-red-100"
        }`}>
          <div className="flex flex-col items-center text-center py-4">
            {paymentDetails.status === "success" ? (
              <CheckCircle className="h-16 w-16 text-green-500 mb-3" />
            ) : (
              <AlertCircle className="h-16 w-16 text-red-500 mb-3" />
            )}

            <CardTitle className="text-2xl md:text-3xl font-bold">
              {paymentDetails.status === "success"
                ? "Payment Successful"
                : "Payment Failed"}
            </CardTitle>

            {paymentDetails.status === "success" && (
              <p className="text-muted-foreground mt-2">
                Thank you for your purchase! Your order has been confirmed and your subscription is being activated automatically.
              </p>
            )}

            {paymentDetails.status !== "success" && (
              <p className="text-muted-foreground mt-2">
                We couldn&apos;t process your payment. Please try again or contact support.
              </p>
            )}
          </div>
        </CardHeader>

        <CardContent className="pt-6 pb-2">
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Payment ID</h3>
                <p className="font-mono text-sm">{paymentDetails.paymentId || "N/A"}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Order ID</h3>
                <p className="font-mono text-sm">{paymentDetails.orderId || "N/A"}</p>
              </div>

              {paymentDetails.email && (
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Email</h3>
                  <p>{paymentDetails.email}</p>
                </div>
              )}

              {paymentDetails.name && (
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Name</h3>
                  <p>{paymentDetails.name}</p>
                </div>
              )}

              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Date</h3>
                <p>{paymentDetails.date}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Payment Method</h3>
                <p>{paymentDetails.gateway}</p>
              </div>
            </div>

            <Separator />

            <div className="flex justify-between items-center py-2">
              <h3 className="text-lg font-semibold">Total Amount</h3>
              <p className="text-lg font-bold">{formatPrice(paymentDetails.amount)}</p>
            </div>
          </div>
        </CardContent>

        <CardFooter className="flex flex-col sm:flex-row gap-4 pt-4">
          <Button asChild className="w-full sm:w-auto">
            <Link href="/">Return to Home</Link>
          </Button>

          {paymentDetails.status === "success" && (
            <Button variant="outline" asChild className="w-full sm:w-auto">
              <Link href={`${process.env.NEXT_PUBLIC_DASHBOARD_URL}/subscriptions`}>Go to Dashboard</Link>
            </Button>
          )}

          {paymentDetails.status !== "success" && (
            <Button variant="outline" asChild className="w-full sm:w-auto">
              <Link href="/checkout">Try Again</Link>
            </Button>
          )}
        </CardFooter>
      </Card>

      {paymentDetails.status === "success" && (
        <div className="mt-8 text-center text-sm text-muted-foreground">
          <p>A confirmation email has been sent to {paymentDetails.email || "your email address"}.</p>
          <p className="mt-1">If you have any questions, please contact our support team.</p>

          {/* Subscription activation status indicator */}
          <div className="mt-4 flex flex-col items-center justify-center">
            <div className="flex items-center space-x-2 text-sm">
              {!manualActivationSuccess &&
               !GLOBAL_ACTIVATION_STATE.PROCESSED_PAYMENTS.has(paymentDetails.paymentId) &&
               !isManuallyActivating && (
                <>
                  <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"></div>
                  <span>Activating your subscription...</span>
                </>
              )}
              {!manualActivationSuccess &&
               !GLOBAL_ACTIVATION_STATE.PROCESSED_PAYMENTS.has(paymentDetails.paymentId) &&
               isManuallyActivating && (
                <>
                  <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full"></div>
                  <span>Finalizing subscription activation...</span>
                </>
              )}
              {(manualActivationSuccess || GLOBAL_ACTIVATION_STATE.PROCESSED_PAYMENTS.has(paymentDetails.paymentId)) && (
                <>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-green-600">Subscription successfully activated!</span>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
