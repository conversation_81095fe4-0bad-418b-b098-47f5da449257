"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { testPlanCodeUtils } from "@/src/utils/plan-utils";
import { 
  <PERSON>, 
  CardContent, 
  CardDescription, 
  <PERSON><PERSON><PERSON>er, 
  Card<PERSON>eader, 
  CardTitle 
} from "@/components/ui/card";
import { CheckCircle, XCircle } from "lucide-react";

export default function PlanUtilsTestPage() {
  const [testResults, setTestResults] = useState<{
    success: boolean;
    results: Array<{
      test: string;
      input: string;
      expected: string;
      actual: string;
      passed: boolean;
    }>;
  } | null>(null);

  const runTests = () => {
    const results = testPlanCodeUtils();
    setTestResults(results);
  };

  return (
    <div className="container py-10">
      <h1 className="text-2xl font-bold mb-6">Plan Utilities Test Page</h1>
      
      <div className="mb-6">
        <Button onClick={runTests}>Run Tests</Button>
      </div>
      
      {testResults && (
        <div className="space-y-4">
          <div className="text-lg font-medium flex items-center gap-2">
            Overall Status: 
            {testResults.success ? (
              <span className="text-green-600 flex items-center">
                <CheckCircle className="w-5 h-5 mr-1" /> All Tests Passed
              </span>
            ) : (
              <span className="text-red-600 flex items-center">
                <XCircle className="w-5 h-5 mr-1" /> Some Tests Failed
              </span>
            )}
          </div>
          
          <div className="grid gap-4 md:grid-cols-2">
            {testResults.results.map((result, index) => (
              <Card key={index} className={result.passed ? "border-green-200" : "border-red-200"}>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base flex items-center justify-between">
                    <span>{result.test}</span>
                    {result.passed ? (
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-600" />
                    )}
                  </CardTitle>
                  <CardDescription>Function: {result.input.includes(",") ? "generatePlanCode" : result.fn}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="font-medium">Input:</div>
                    <div className="font-mono">{result.input}</div>
                    
                    <div className="font-medium">Expected:</div>
                    <div className="font-mono">{result.expected}</div>
                    
                    <div className="font-medium">Actual:</div>
                    <div className={`font-mono ${!result.passed ? "text-red-500" : ""}`}>
                      {result.actual}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 