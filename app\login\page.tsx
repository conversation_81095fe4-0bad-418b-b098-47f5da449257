'use client';

import { useState, useEffect } from 'react';
import { ArrowRight, Lock } from 'lucide-react';
import { cn } from '@/lib/utils';
import { InteractiveGridPattern } from '@/components/magicui/interactive-grid-pattern';
import { AuroraText } from '@/components/magicui/aurora-text';
import { signIn } from 'next-auth/react';
import { useSearchParams, useRouter } from 'next/navigation';

const LoginPage = () => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();

  // On mount, check if there is an error in the URL (e.g. from a failed login attempt)
  useEffect(() => {
    const errorParam = searchParams.get('error');
    if (errorParam) {
      setError("Invalid credentials. Please try again.");
    }
  }, [searchParams]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    
    // Check if phoneNumber already starts with "91"
    const formattedUsername = phoneNumber.startsWith("91") 
      ? phoneNumber 
      : `91${phoneNumber}`;
    
    // Get the return URL from the query parameters or default to the home page
    const returnUrl = searchParams.get('returnUrl') || '/';
    
    // Log the final formatted username and password for debugging
    console.log('Login attempt body:', {
      username: formattedUsername,
      password,
    });
    
    // Disable redirect so that we can handle errors
    const result = await signIn('credentials', {
      username: formattedUsername,
      password,
      redirect: false,
    }) as { error?: string } | undefined;
    
    if (result?.error) {
      console.error('Sign in error:', result.error);
      localStorage.setItem('loginErrorDetail', result.error);
      setError("Invalid credentials. Please try again.");
      setIsLoading(false);
    } else {
      // Successful login - redirect to the return URL
      router.push(returnUrl);
    }
  };

  return (
    <section className="relative w-full bg-background/95 dark:bg-background min-h-screen isolate overflow-hidden">
      <div className="absolute inset-0 -z-10">
        <InteractiveGridPattern
          className={cn(
            "[mask-image:radial-gradient(600px_circle_at_center,white,transparent)]",
            "absolute inset-x-0 inset-y-[-30%] h-[160%] w-full opacity-20 dark:opacity-30 skew-y-12"
          )} 
          squares={[20, 20]}
          width={60}
          height={60}
        />
      </div>

      <div className="container relative flex items-center justify-center min-h-screen p-4">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold tracking-tight mb-4">
              <AuroraText className="text-primary">Welcome Back</AuroraText>
            </h1>
            <p className="text-lg text-muted-foreground">
              Enter your credentials to access the platform
            </p>
          </div>

          <div className="bg-card/50 backdrop-blur-sm border rounded-2xl p-8 shadow-lg">
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div>
                <label htmlFor="phoneNumber" className="block text-sm font-medium text-card-foreground mb-2">
                  Phone Number
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">+91</span>
                  <input
                    type="text"
                    id="phoneNumber"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    className="w-full px-4 py-3 pl-14 rounded-lg bg-background/50 border border-input ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 transition-all"
                    placeholder="Enter phone number"
                  />
                </div>
              </div>
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-card-foreground mb-2">
                  Password
                </label>
                <div className="relative">
                  <input
                    type="password"
                    id="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full px-4 py-3 pl-10 rounded-lg bg-background/50 border border-input ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 transition-all"
                    placeholder="Enter password"
                  />
                  <Lock className="absolute left-3 top-1/2 -translate-y-1/2 size-4 text-muted-foreground" />
                </div>
              </div>
              
              {error && (
                <p className="text-destructive text-sm">{error}</p>
              )}

              <button
                type="submit"
                disabled={isLoading}
                className={cn(
                  "w-full inline-flex items-center justify-center px-6 py-3 rounded-lg bg-primary text-primary-foreground font-medium",
                  "transition-colors hover:bg-primary/90 disabled:opacity-50 group"
                )}
              >
                {isLoading ? 'Loading...' : 'Login'}
                <ArrowRight className="ml-2 size-4 transition-transform group-hover:translate-x-0.5" />
              </button>
            </form>
            <p className="text-center mt-4">
              Don&apos;t have an account? <a href="/signup" className="text-primary">Create Account</a>
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LoginPage;