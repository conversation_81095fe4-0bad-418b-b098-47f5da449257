"use client";

import { useEffect, useState, useCallback } from "react";
import { useUser } from "@/context/user-context";
import { toast } from "sonner";
import {
  getPendingInvoicesForCustomer,
  updateInvoicePaymentStatus,
} from "@/src/services/lago/invoiceService";

interface AutoPaymentStatusUpdaterProps {
  onComplete?: (result: {
    success: boolean;
    updatedCount: number;
    failedCount: number;
    emailSent?: boolean;
  }) => void;
  dateFrom?: string;
  enabled?: boolean;
  paymentId?: string;
  sendEmail?: boolean;
}

/**
 * Component that automatically checks for pending invoices and updates their status
 *
 * This component doesn't render anything visible but performs the check
 * and update operations in the background. It's useful for automatically
 * handling pending invoices after payment.
 */
export function AutoPaymentStatusUpdater({
  onComplete,
  dateFrom,
  enabled = true,
  paymentId,
  sendEmail = true
}: AutoPaymentStatusUpdaterProps) {
  const { userId } = useUser();
  const [isProcessing, setIsProcessing] = useState(false);
  const [hasChecked, setHasChecked] = useState(false);

  // Function to log processing steps with timestamps
  const logProcessingStep = useCallback((message: string, data?: any) => {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] AUTO PAYMENT STATUS - ${message}`;

    if (data) {
      console.log(logMessage, data);
    } else {
      console.log(logMessage);
    }
  }, []);

  // Function to check and update pending invoices
  const checkAndUpdatePendingInvoices = useCallback(async () => {
    if (!userId || !enabled || isProcessing || hasChecked) {
      return;
    }

    logProcessingStep(`Starting automatic payment status update for user: ${userId}`);
    setIsProcessing(true);

    try {
      // Use the dateFrom parameter if provided, otherwise use current date
      const formattedDate = dateFrom || new Date().toISOString().split('T')[0];

      // Wait a bit to ensure invoices are generated
      logProcessingStep('Waiting for invoice generation...');
      await new Promise(resolve => setTimeout(resolve, 5000));

      // Get pending invoices
      logProcessingStep(`Checking for pending invoices from date: ${formattedDate}`);
      const pendingInvoices = await getPendingInvoicesForCustomer(userId, formattedDate);

      if (!pendingInvoices || pendingInvoices.length === 0) {
        logProcessingStep('No pending invoices found');
        setHasChecked(true);

        if (onComplete) {
          onComplete({
            success: true,
            updatedCount: 0,
            failedCount: 0
          });
        }

        return;
      }

      logProcessingStep(`Found ${pendingInvoices.length} pending invoices`, {
        invoiceIds: pendingInvoices.map(inv => inv.lago_id)
      });

      // Update each invoice
      let successCount = 0;
      let failedCount = 0;

      // Generate a tracking ID for this batch of updates
      const trackingId = `auto_${Date.now()}_${paymentId || 'nopayment'}`;

      for (const invoice of pendingInvoices) {
        try {
          logProcessingStep(`Updating invoice ${invoice.lago_id}`);
          const updateResult = await updateInvoicePaymentStatus(
            invoice.lago_id,
            sendEmail, // Whether to send email notification
            trackingId // Tracking ID for logging
          );

          if (updateResult) {
            logProcessingStep(`Successfully updated invoice ${invoice.lago_id}`);
            successCount++;
          } else {
            logProcessingStep(`Failed to update invoice ${invoice.lago_id}`);
            failedCount++;
          }
        } catch (error) {
          logProcessingStep(`Error updating invoice ${invoice.lago_id}`, error);
          failedCount++;
        }

        // Small delay between requests to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // Log results
      if (successCount > 0) {
        logProcessingStep(`Successfully updated ${successCount}/${pendingInvoices.length} invoices`);

        if (paymentId) {
          // Store in localStorage that we've updated invoices for this payment
          try {
            localStorage.setItem(`payment_invoices_updated_${paymentId}`, 'true');
          } catch (error) {
            console.error('Error storing payment invoice update status:', error);
          }
        }

        // Show toast only if we updated at least one invoice
        toast.success(`Updated ${successCount} invoice payment status`);
      } else if (failedCount > 0) {
        logProcessingStep(`Failed to update any invoices (${failedCount} failures)`);
        toast.error("Failed to update invoice payment status");
      }

      // Call onComplete callback if provided
      if (onComplete) {
        onComplete({
          success: failedCount === 0,
          updatedCount: successCount,
          failedCount,
          emailSent: sendEmail && successCount > 0
        });
      }

      setHasChecked(true);
    } catch (error) {
      logProcessingStep("Error checking/updating pending invoices", error);

      if (onComplete) {
        onComplete({
          success: false,
          updatedCount: 0,
          failedCount: 1
        });
      }
    } finally {
      setIsProcessing(false);
    }
  }, [userId, dateFrom, enabled, isProcessing, hasChecked, onComplete, logProcessingStep, paymentId]);

  // Effect to automatically check and update pending invoices
  useEffect(() => {
    if (enabled && userId && !hasChecked && !isProcessing) {
      checkAndUpdatePendingInvoices();
    }
  }, [enabled, userId, hasChecked, isProcessing, checkAndUpdatePendingInvoices]);

  // This component doesn't render anything
  return null;
}
