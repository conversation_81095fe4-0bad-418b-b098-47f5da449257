import { useMemo } from 'react';
import { 
  extractProductCode, 
  extractPlanType, 
  extractPlanDuration,
  isTrialPlan,
  getProductDisplayName,
  getPlanTypeDisplayName,
  getPlanDurationDisplayName
} from '@/src/utils/plan-utils';

export interface FormattedSubscriptionDetails {
  productCode: string;
  productName: string;
  planType: string | null;
  planTypeName: string | null;
  planDuration: string | null;
  planDurationName: string | null;
  isTrial: boolean;
  expiresAt: Date | null;
  startedAt: Date | null;
  status: string;
  formattedName: string;
}

interface UseSubscriptionDetailsOptions {
  includeProduct?: boolean;
  includeType?: boolean;
  includeDuration?: boolean;
  includeStatus?: boolean;
}

export function useSubscriptionDetails(
  planCode: string | undefined | null,
  status?: string,
  startedAt?: string | null,
  expiresAt?: string | null,
  options: UseSubscriptionDetailsOptions = {}
): FormattedSubscriptionDetails | null {
  const {
    includeProduct = true,
    includeType = true,
    includeDuration = true,
    includeStatus = true,
  } = options;

  return useMemo(() => {
    if (!planCode) return null;

    const productCode = extractProductCode(planCode);
    const planType = extractPlanType(planCode);
    const planDuration = extractPlanDuration(planCode);
    const trial = isTrialPlan(planCode);

    // Format the display name based on options
    const parts: string[] = [];
    
    if (includeProduct) {
      parts.push(getProductDisplayName(productCode));
    }
    
    if (includeType && planType) {
      parts.push(getPlanTypeDisplayName(planType));
    }
    
    if (includeDuration && planDuration) {
      parts.push(getPlanDurationDisplayName(planDuration));
    }
    
    if (includeStatus && status) {
      // Capitalize the first letter of status
      parts.push(status.charAt(0).toUpperCase() + status.slice(1));
    }

    if (trial) {
      parts.push('Trial');
    }

    return {
      productCode,
      productName: getProductDisplayName(productCode),
      planType,
      planTypeName: planType ? getPlanTypeDisplayName(planType) : null,
      planDuration,
      planDurationName: planDuration ? getPlanDurationDisplayName(planDuration) : null,
      isTrial: trial,
      status: status || 'unknown',
      startedAt: startedAt ? new Date(startedAt) : null,
      expiresAt: expiresAt ? new Date(expiresAt) : null,
      formattedName: parts.join(' ')
    };
  }, [planCode, status, startedAt, expiresAt, includeProduct, includeType, includeDuration, includeStatus]);
} 