import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { checkUserSubscription } from '@/src/utils/subscription-utils';

/**
 * Custom hook to check if a user is subscribed to a product
 * @param planCode The plan code to check
 * @returns Object with subscription status and loading state
 */
export function useSubscriptionCheck(planCode: string) {
  const { data: session, status } = useSession();
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [subscription, setSubscription] = useState<unknown>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkSubscription = async () => {
      // Only check if user is authenticated and we have a plan code
      if (status !== 'authenticated' || !session?.user?.id || !planCode) {
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const result = await checkUserSubscription(session.user.id, planCode);
        setIsSubscribed(result.isSubscribed);
        setSubscription(result.subscription);
      } catch (err) {
        console.error('Error checking subscription:', err);
        setError('Failed to check subscription status');
      } finally {
        setIsLoading(false);
      }
    };

    checkSubscription();
  }, [status, session, planCode]);

  return {
    isSubscribed,
    subscription,
    isLoading,
    error,
    isAuthenticated: status === 'authenticated',
    userId: session?.user?.id
  };
}
